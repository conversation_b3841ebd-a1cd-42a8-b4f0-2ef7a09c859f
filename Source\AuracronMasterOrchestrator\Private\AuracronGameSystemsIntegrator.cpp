/**
 * AuracronGameSystemsIntegrator.cpp
 * 
 * Sistema integrador final que conecta e coordena todos os sistemas
 * implementados para criar uma experiência de jogo completa e otimizada.
 */

#include "AuracronGameSystemsIntegrator.h"
#include "AuracronMasterOrchestrator.h"
#include "Engine/World.h"
#include "TimerManager.h"

void UAuracronGameSystemsIntegrator::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    bIntegrationInitialized = false;
    InitializationStartTime = 0.0f;
    RecoveryAttempts = 0;
    CurrentIntegrationState = ESystemIntegrationState::Uninitialized;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Game Systems Integrator initialized"));
}

void UAuracronGameSystemsIntegrator::Deinitialize()
{
    ShutdownSystemIntegration();
    Super::Deinitialize();
}

void UAuracronGameSystemsIntegrator::InitializeSystemIntegration()
{
    if (bIntegrationInitialized)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Starting system integration"));
    
    CurrentIntegrationState = ESystemIntegrationState::Initializing;
    InitializationStartTime = GetWorld()->GetTimeSeconds();
    
    // Setup system dependencies
    SetupSystemDependencies();
    
    // Initialize systems in order
    InitializeSystemsInOrder();
    
    // Start health check timer
    StartHealthCheckTimer();
    
    bIntegrationInitialized = true;
    CurrentIntegrationState = ESystemIntegrationState::FullyIntegrated;
    
    OnIntegrationCompleted();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: System integration completed"));
}

void UAuracronGameSystemsIntegrator::InitializeAllSystems()
{
    // Initialize all registered systems
    for (auto& SystemPair : SystemStatuses)
    {
        InitializeSystem(SystemPair.Key);
    }
}

void UAuracronGameSystemsIntegrator::ShutdownSystemIntegration()
{
    if (!bIntegrationInitialized)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Shutting down system integration"));
    
    // Clear timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(HealthCheckTimer);
        GetWorld()->GetTimerManager().ClearTimer(InitializationTimer);
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimer);
    }
    
    // Reset state
    bIntegrationInitialized = false;
    CurrentIntegrationState = ESystemIntegrationState::Shutdown;
    SystemStatuses.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: System integration shutdown complete"));
}

void UAuracronGameSystemsIntegrator::PerformSystemHealthCheck()
{
    for (auto& SystemPair : SystemStatuses)
    {
        PerformIndividualHealthCheck(SystemPair.Key);
    }
    
    UpdateIntegrationMetrics();
}

bool UAuracronGameSystemsIntegrator::InitializeSystem(const FString& SystemName)
{
    return InitializeSystemInternal(SystemName);
}

bool UAuracronGameSystemsIntegrator::RestartSystem(const FString& SystemName)
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Restarting system: %s"), *SystemName);
    
    // Update system status to not initialized
    UpdateSystemStatus(SystemName, false, false, TEXT("Restarting"));
    
    // Attempt to initialize again
    bool bSuccess = InitializeSystemInternal(SystemName);
    
    if (bSuccess)
    {
        OnSystemRecovered(SystemName);
    }
    
    return bSuccess;
}

FSystemStatus UAuracronGameSystemsIntegrator::GetSystemStatus(const FString& SystemName) const
{
    if (const FSystemStatus* Status = SystemStatuses.Find(SystemName))
    {
        return *Status;
    }
    
    // Return default status if not found
    FSystemStatus DefaultStatus;
    DefaultStatus.SystemName = SystemName;
    DefaultStatus.ErrorMessage = TEXT("System not registered");
    return DefaultStatus;
}

TMap<FString, FSystemStatus> UAuracronGameSystemsIntegrator::GetAllSystemStatuses() const
{
    return SystemStatuses;
}

bool UAuracronGameSystemsIntegrator::IsSystemInitialized(const FString& SystemName) const
{
    if (const FSystemStatus* Status = SystemStatuses.Find(SystemName))
    {
        return Status->bInitialized;
    }
    return false;
}

bool UAuracronGameSystemsIntegrator::AreAllSystemsReady() const
{
    for (const auto& SystemPair : SystemStatuses)
    {
        if (!SystemPair.Value.bInitialized || !SystemPair.Value.bHealthy)
        {
            return false;
        }
    }
    return true;
}

ESystemIntegrationState UAuracronGameSystemsIntegrator::GetIntegrationState() const
{
    return CurrentIntegrationState;
}

FIntegrationMetrics UAuracronGameSystemsIntegrator::GetIntegrationMetrics() const
{
    return CurrentMetrics;
}

float UAuracronGameSystemsIntegrator::GetInitializationProgress() const
{
    return CalculateInitializationProgress();
}

void UAuracronGameSystemsIntegrator::ConfigureIntegration(const FSystemIntegrationConfig& Config)
{
    IntegrationConfig = Config;
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Integration configuration updated"));
}

FSystemIntegrationConfig UAuracronGameSystemsIntegrator::GetCurrentConfiguration() const
{
    return IntegrationConfig;
}

void UAuracronGameSystemsIntegrator::OptimizeSystemsForHardware()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Optimizing systems for hardware"));
    ApplyHardwareOptimizations();
}

void UAuracronGameSystemsIntegrator::ApplyDynamicAdaptation()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying dynamic adaptation"));
    AdjustSystemPriorities();
}

void UAuracronGameSystemsIntegrator::BalanceSystemResources()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Balancing system resources"));
    OptimizeResourceAllocation();
}

bool UAuracronGameSystemsIntegrator::RecoverFailedSystem(const FString& SystemName)
{
    return AttemptSystemRecovery(SystemName);
}

void UAuracronGameSystemsIntegrator::RecoverAllFailedSystems()
{
    for (auto& SystemPair : SystemStatuses)
    {
        if (!SystemPair.Value.bHealthy)
        {
            AttemptSystemRecovery(SystemPair.Key);
        }
    }
}

void UAuracronGameSystemsIntegrator::ValidateAndFixDependencies()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Validating and fixing dependencies"));
    
    for (auto& SystemPair : SystemStatuses)
    {
        CheckSystemDependencies(SystemPair.Key);
    }
}

// Private implementation methods
void UAuracronGameSystemsIntegrator::SetupSystemDependencies()
{
    // Register core systems
    RegisterSystemStatus(TEXT("MasterOrchestrator"), ESystemInitializationPriority::Critical);
    RegisterSystemStatus(TEXT("HardwareDetection"), ESystemInitializationPriority::High);
    RegisterSystemStatus(TEXT("DynamicRealm"), ESystemInitializationPriority::Normal);
    RegisterSystemStatus(TEXT("HarmonyEngine"), ESystemInitializationPriority::Normal);
    RegisterSystemStatus(TEXT("SigilosBridge"), ESystemInitializationPriority::Low);
    RegisterSystemStatus(TEXT("PCGBridge"), ESystemInitializationPriority::Low);
}

void UAuracronGameSystemsIntegrator::InitializeSystemsInOrder()
{
    // Initialize systems based on priority
    TArray<FString> SystemsToInitialize;
    
    // Critical systems first
    for (const auto& SystemPair : SystemStatuses)
    {
        if (SystemPair.Value.Priority == ESystemInitializationPriority::Critical)
        {
            SystemsToInitialize.Add(SystemPair.Key);
        }
    }
    
    // Then high priority
    for (const auto& SystemPair : SystemStatuses)
    {
        if (SystemPair.Value.Priority == ESystemInitializationPriority::High)
        {
            SystemsToInitialize.Add(SystemPair.Key);
        }
    }
    
    // Then normal and low priority
    for (const auto& SystemPair : SystemStatuses)
    {
        if (SystemPair.Value.Priority == ESystemInitializationPriority::Normal ||
            SystemPair.Value.Priority == ESystemInitializationPriority::Low)
        {
            SystemsToInitialize.Add(SystemPair.Key);
        }
    }
    
    // Initialize each system
    for (const FString& SystemName : SystemsToInitialize)
    {
        InitializeSystemInternal(SystemName);
    }
}

void UAuracronGameSystemsIntegrator::ValidateSystemIntegration()
{
    // Validate that all systems are properly integrated
    bool bAllSystemsValid = true;
    
    for (const auto& SystemPair : SystemStatuses)
    {
        if (!SystemPair.Value.bInitialized)
        {
            bAllSystemsValid = false;
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: System %s is not initialized"), *SystemPair.Key);
        }
    }
    
    if (bAllSystemsValid)
    {
        CurrentIntegrationState = ESystemIntegrationState::FullyIntegrated;
    }
    else
    {
        CurrentIntegrationState = ESystemIntegrationState::PartiallyReady;
    }
}

void UAuracronGameSystemsIntegrator::StartHealthCheckTimer()
{
    if (GetWorld() && IntegrationConfig.bEnableAutoHealthCheck)
    {
        GetWorld()->GetTimerManager().SetTimer(
            HealthCheckTimer,
            this,
            &UAuracronGameSystemsIntegrator::PerformSystemHealthCheck,
            IntegrationConfig.HealthCheckInterval,
            true
        );
    }
}
